#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化回滚脚本
将系统恢复到优化前的状态
"""

import os
import shutil
import json
from datetime import datetime

def rollback_optimization():
    """执行优化回滚"""
    print("🔄 开始执行优化回滚...")
    print("=" * 60)
    
    backup_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(backup_dir)
    
    # 回滚文件映射
    rollback_files = {
        'core_monitor_house_monitor.py': 'core/monitor/house_monitor.py',
        'monitor_service_core_monitor.py': 'monitor_service/core/monitor.py',
        'user_config.json': 'user_config.json'
    }
    
    success_count = 0
    total_count = len(rollback_files)
    
    # 执行文件回滚
    for backup_file, target_file in rollback_files.items():
        backup_path = os.path.join(backup_dir, backup_file)
        target_path = os.path.join(project_root, target_file)
        
        try:
            if os.path.exists(backup_path):
                # 创建目标目录（如果不存在）
                target_dir = os.path.dirname(target_path)
                os.makedirs(target_dir, exist_ok=True)
                
                # 复制备份文件到目标位置
                shutil.copy2(backup_path, target_path)
                print(f"✅ 已回滚: {target_file}")
                success_count += 1
            else:
                print(f"❌ 备份文件不存在: {backup_file}")
        except Exception as e:
            print(f"❌ 回滚失败 {target_file}: {e}")
    
    print("=" * 60)
    print(f"📊 回滚结果: {success_count}/{total_count} 个文件成功回滚")
    
    if success_count == total_count:
        print("🎉 优化回滚完成！系统已恢复到优化前状态")
        print("\n📋 回滚后的系统状态:")
        print("- 监控模式: 传统模式")
        print("- 监控间隔: 60秒")
        print("- API调用: 双重调用（数量+详情）")
        print("- 监控方法: monitor_async")
        
        print("\n🔧 下一步操作:")
        print("1. 重启监控服务以使更改生效")
        print("2. 检查监控日志确认传统模式正常工作")
        print("3. 如需重新启用优化，请重新运行优化实施脚本")
        
        return True
    else:
        print("⚠️ 部分文件回滚失败，请手动检查")
        return False

def verify_rollback():
    """验证回滚结果"""
    print("\n🔍 验证回滚结果...")
    
    try:
        # 检查配置文件
        with open('user_config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        optimization = config.get('monitoring_optimization', {})
        check_interval = config.get('check_interval', 60)
        
        print(f"✅ 配置文件状态:")
        print(f"  - 优化启用: {optimization.get('enabled', False)}")
        print(f"  - 监控模式: {optimization.get('mode', 'legacy')}")
        print(f"  - 监控间隔: {check_interval}秒")
        
        # 检查代码文件
        house_monitor_path = 'core/monitor/house_monitor.py'
        if os.path.exists(house_monitor_path):
            with open(house_monitor_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            has_optimization = 'monitor_by_details_async' in content
            print(f"✅ 代码文件状态:")
            print(f"  - 优化方法存在: {has_optimization}")
            
            if not has_optimization:
                print("✅ 回滚验证成功：优化代码已移除")
            else:
                print("⚠️ 注意：优化代码仍然存在，但已禁用")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def create_rollback_log():
    """创建回滚日志"""
    log_content = f"""
优化回滚日志
============

回滚时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
回滚目录: {os.path.dirname(os.path.abspath(__file__))}

回滚的文件:
- core/monitor/house_monitor.py (移除优化监控方法)
- monitor_service/core/monitor.py (恢复传统等待逻辑)
- user_config.json (禁用优化配置)

回滚后状态:
- 监控模式: 传统模式
- 监控间隔: 60秒
- API调用方式: 双重调用
- 优化功能: 已禁用

如需重新启用优化，请运行优化实施脚本。
"""
    
    try:
        with open('rollback.log', 'w', encoding='utf-8') as f:
            f.write(log_content)
        print("📝 回滚日志已创建: rollback.log")
    except Exception as e:
        print(f"⚠️ 创建回滚日志失败: {e}")

def main():
    """主函数"""
    print("🔄 优化回滚工具")
    print("此工具将系统恢复到优化前的状态")
    print()
    
    # 确认操作
    confirm = input("确定要执行回滚操作吗？(y/N): ").strip().lower()
    if confirm not in ['y', 'yes']:
        print("❌ 回滚操作已取消")
        return
    
    try:
        # 执行回滚
        success = rollback_optimization()
        
        if success:
            # 验证回滚
            verify_rollback()
            
            # 创建日志
            create_rollback_log()
            
            print("\n✅ 回滚操作完成！")
        else:
            print("\n❌ 回滚操作失败，请检查错误信息")
            
    except Exception as e:
        print(f"\n❌ 回滚过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
