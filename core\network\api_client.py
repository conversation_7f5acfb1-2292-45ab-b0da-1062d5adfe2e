"""API客户端 - 专门处理房源相关的API调用"""

from typing import Dict, List, Optional, Tuple, Any
import asyncio

from .http_client import OptimizedHttpClient
from .proxy_manager import ProxyManager
from ..data.models import HouseDetail
from ..utils.unified_logging import Logger
from ..utils.helpers import retry_on_exception


class ApiClient:
    """房源API客户端 - 使用优化的HTTP连接池"""

    def __init__(self, http_client: OptimizedHttpClient, base_url: str, common_params: Dict[str, str],
                 proxy_manager: Optional[ProxyManager] = None, logger: Optional[Logger] = None,
                 notification_manager=None, config: Optional[Dict[str, Any]] = None):
        self.http_client = http_client
        self.base_url = base_url
        self.common_params = common_params
        self.proxy_manager = proxy_manager or http_client.proxy_manager  # 从http_client获取proxy_manager
        self.logger = logger or Logger("ApiClient")
        self.notification_manager = notification_manager
        self.config = config or {}

        # API错误监控
        self.error_count = 0
        self.consecutive_errors = 0
        self.first_error_time = 0
        self.last_admin_notification_time = 0  # 最后一次管理员通知时间
        self.has_sent_error_notification = False  # 是否已发送错误通知

        # 从配置中获取监控参数
        self.api_error_config = self.config.get('api_error_monitoring', {})
        self.admin_notifier_config = self.config.get('admin_notifier', {})

        # 性能监控
        self.performance_report_interval = 100  # 每100个请求报告一次性能

        # 代理更新防重机制 - 5秒内只允许一次异常处理的代理更新
        self._last_proxy_exception_handle_time = 0
        self._proxy_exception_handle_cooldown = 5.0  # 5秒冷却时间

    @retry_on_exception(max_retries=3, exceptions=(Exception,))
    async def fetch_house_count_async(self, house_name: str) -> Tuple[Optional[int], Optional[str]]:
        """异步获取房源数量和小区ID"""
        try:
            payload = {"type": "保障性住宅小区", "name": house_name}

            async with await self.http_client.post_async(
                self.base_url,
                params=self.common_params,
                json=payload
            ) as response:
                data = await response.json()

                # 记录API成功
                await self._record_api_success()

                if data.get("errcode") != 0:
                    error_msg = f"API返回错误码: {data.get('errcode')}"
                    self.logger.warning(f"[{house_name}] {error_msg}")
                    await self._record_api_error(f"错误码:{data.get('errcode')}", house_name)
                    return None, None

                # 检查数据结构完整性
                datas = data.get("data", {}).get("datas", [])
                if not datas or len(datas) == 0:
                    self.logger.warning(f"[{house_name}] API返回空的数据集，可能该房源不存在或暂无数据")
                    self.logger.debug(f"[{house_name}] API响应详情: {data}")
                    await self._record_api_error("空数据集", house_name)
                    return None, None

                # 解析响应数据
                estate_data = datas[0]
                households = estate_data["itemmap"]["households"]
                estate_id = estate_data["id"]

                return int(households), estate_id

        except asyncio.TimeoutError as e:
            self.logger.warning(f"[{house_name}] 请求超时")
            await self._record_api_error("TimeoutError", house_name)
            # 立即尝试刷新代理
            await self._handle_proxy_exception("TimeoutError", str(e))
            raise
        except Exception as e:
            exception_type = type(e).__name__
            self.logger.error(f"[{house_name}] 获取房源数量失败: {exception_type} - {str(e)}")
            await self._record_api_error(exception_type, house_name)
            # 立即尝试刷新代理
            await self._handle_proxy_exception(exception_type, str(e))
            raise

    async def _handle_proxy_exception(self, exception_type: str, exception_msg: str):
        """处理可能的代理相关异常 - 含防重机制"""
        if self.proxy_manager:
            # 检查是否应该触发代理更新（防重机制）
            import time
            current_time = time.time()
            if current_time - self._last_proxy_exception_handle_time >= self._proxy_exception_handle_cooldown:
                self._last_proxy_exception_handle_time = current_time
                try:
                    await self.proxy_manager.force_refresh_proxy_on_exception(exception_type, exception_msg)
                except Exception as refresh_error:
                    self.logger.error(f"代理刷新过程中出错: {str(refresh_error)}")
            else:
                remaining_time = self._proxy_exception_handle_cooldown - (current_time - self._last_proxy_exception_handle_time)
                self.logger.debug(f"异常处理代理更新请求被限制，需等待 {remaining_time:.1f} 秒")

    async def _record_api_error(self, error_type: str, house_name: str = ""):
        """记录API错误"""
        import time

        current_time = time.time()
        self.error_count += 1
        self.consecutive_errors += 1

        # 如果是第一次错误，记录开始时间
        if self.first_error_time == 0:
            self.first_error_time = current_time
            self.logger.warning(f"开始记录API错误，错误类型: {error_type}", house_name=house_name)

        # 检查是否需要发送管理员通知
        await self._check_and_send_admin_notification(current_time, error_type, house_name)

    async def _check_and_send_admin_notification(self, current_time: float, error_type: str, house_name: str):
        """检查并发送管理员通知"""
        if not self.notification_manager or not self.admin_notifier_config.get('enabled', True):
            return

        # 获取配置参数
        consecutive_threshold = self.api_error_config.get('consecutive_error_threshold', 300)
        duration_threshold = self.api_error_config.get('error_duration_threshold', 180)
        notification_interval = self.api_error_config.get('notification_interval', 300)

        # 计算错误持续时间
        error_duration = current_time - self.first_error_time if self.first_error_time > 0 else 0

        # 检查是否满足通知条件
        should_notify = False
        notify_reason = ""

        if self.consecutive_errors >= consecutive_threshold:
            should_notify = True
            notify_reason = f"连续错误次数达到 {consecutive_threshold} 次"
        elif error_duration >= duration_threshold:
            should_notify = True
            notify_reason = f"错误持续时间达到 {duration_threshold} 秒"

        # 检查通知间隔
        if should_notify and (current_time - self.last_admin_notification_time) >= notification_interval:
            await self._send_error_notification(notify_reason, error_type, house_name)
            self.last_admin_notification_time = current_time
            self.has_sent_error_notification = True

    async def _send_error_notification(self, reason: str, error_type: str, house_name: str):
        """发送错误通知给管理员"""
        try:
            import time
            current_time_str = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            error_duration = time.time() - self.first_error_time if self.first_error_time > 0 else 0

            title = " API监控异常警报"
            content = (
                f"时间: {current_time_str}\n"
                f"警报原因: {reason}\n"
                f"错误类型: {error_type}\n"
                f"影响房源: {house_name or '所有房源'}\n"
                f"连续错误次数: {self.consecutive_errors}\n"
                f"错误持续时间: {int(error_duration)} 秒\n"
                f"总错误次数: {self.error_count}\n\n"
                f"请检查网络连接、代理设置或目标网站状态。"
            )

            device_ids = self.admin_notifier_config.get('device_ids', [])
            if device_ids:
                await self.notification_manager.send_admin_notification(title, content)
                self.logger.warning(f"已向管理员发送API错误通知: {reason}")
            else:
                self.logger.warning("未配置管理员设备ID，无法发送错误通知")

        except Exception as e:
            self.logger.error(f"发送管理员错误通知失败: {str(e)}")

    async def _send_recovery_notification(self):
        """发送恢复通知给管理员"""
        if not self.api_error_config.get('recovery_notification', True):
            return

        try:
            import time
            current_time_str = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())

            title = "✅ API监控恢复正常"
            content = (
                f"时间: {current_time_str}\n"
                f"API访问已恢复正常\n"
                f"之前连续错误次数: {self.consecutive_errors}\n"
                f"错误持续时间: {int(time.time() - self.first_error_time)} 秒\n"
                f"监控服务继续正常运行。"
            )

            device_ids = self.admin_notifier_config.get('device_ids', [])
            if device_ids and self.has_sent_error_notification:
                await self.notification_manager.send_admin_notification(title, content)
                self.logger.info("已向管理员发送API恢复通知")

        except Exception as e:
            self.logger.error(f"发送管理员恢复通知失败: {str(e)}")

    async def _record_api_success(self):
        """记录API成功响应"""
        if self.consecutive_errors > 0:
            self.logger.info(f"API恢复正常，持续了 {self.consecutive_errors} 次错误")

            # 如果之前发送了错误通知，现在发送恢复通知
            if self.has_sent_error_notification:
                await self._send_recovery_notification()

            # 重置错误相关计数器
            self.consecutive_errors = 0
            self.first_error_time = 0
            self.has_sent_error_notification = False

    @retry_on_exception(max_retries=3, exceptions=(Exception,))
    async def fetch_house_details_async(self, estate_id: str) -> List[HouseDetail]:
        """
        异步获取房源详细信息 (优化版)

        直接调用房源详情API，获取小区内所有房源的详细信息
        用于优化监控模式：基于房源ID集合变化检测新增房源

        Args:
            estate_id: 小区ID (如: GDsChydOYgeYJg7NADs)

        Returns:
            List[HouseDetail]: 房源详情列表，每个房源包含唯一ID
        """
        try:
            # 构建请求参数，完全匹配实际API格式
            params = {
                "application": "jqHj7ddxI1smOEkmKSD",
                "apiview": "house",
                "_localversion": "",  # 保持空值
                "domainId": "LAMDyAh4HSdnug1KdKL"
            }

            # 请求体，完全匹配你提供的格式
            payload = {
                "lines": 12,  # 匹配实际请求格式
                "direction": "",
                "houselooks": "",
                "min_rentMonth": "",
                "max_rentMonth": "",
                "houseType": "保障房",
                "status": "已上架",
                "houseEstateid": estate_id  # 小区ID，如: GDsChydOYgeYJg7NADs
            }

            self.logger.debug(f"🔍 [优化模式] 直接获取小区 {estate_id} 的房源详情")

            async with await self.http_client.post_async(
                "https://www.huhhothome.cn/api/dynamicapi/apiview/viewdata",
                params=params,
                json=payload,
                timeout=10
            ) as response:
                data = await response.json()

                # 记录API成功
                await self._record_api_success()

                if data.get("errcode") != 0.0:  # 匹配实际响应格式 (0.0)
                    error_msg = f"API返回错误码: {data.get('errcode')}"
                    self.logger.warning(f"获取房源详情失败: {error_msg}")
                    await self._record_api_error(f"错误码:{data.get('errcode')}", f"获取房源详情")
                    return []

                # 提取房源信息，重点关注房源ID
                response_data = data.get("data", {})
                houses = response_data.get("datas", [])
                rowcount = response_data.get("rowcount", 0)

                self.logger.debug(f"📊 API响应: 总计 {rowcount} 个房源，返回 {len(houses)} 个详情")

                house_details = []

                for house in houses:
                    try:
                        # 提取房源唯一ID (关键字段)
                        house_id = house.get("id", "")
                        if not house_id:
                            self.logger.warning("发现房源缺少ID字段，跳过")
                            continue

                        itemmap = house.get("itemmap", {})

                        # 构建房源详情对象
                        house_detail = HouseDetail(
                            id=house_id,  # 房源唯一ID，如: gVkDC8OeUw02bXnOCrX
                            name=itemmap.get("houseestatename", "未知"),
                            position=itemmap.get("position", "未知"),
                            roomno=itemmap.get("roomno", "未知"),
                            outtype=itemmap.get("outtype", "未知"),
                            direction=itemmap.get("direction", "未知"),
                            area=float(itemmap.get("area", 0)),
                            rent=float(itemmap.get("rentmonth", 0))
                        )
                        house_details.append(house_detail)

                    except Exception as e:
                        self.logger.warning(f"处理房源详情时出错: {str(e)}")
                        continue

                # 提取所有房源ID用于日志
                house_ids = [h.id for h in house_details]
                self.logger.debug(f"✅ 成功获取 {len(house_details)} 个房源详情，ID列表: {house_ids[:3]}{'...' if len(house_ids) > 3 else ''}")

                return house_details

        except asyncio.TimeoutError as e:
            self.logger.warning(f"获取房源详情请求超时")
            await self._record_api_error("TimeoutError", f"获取房源详情")
            # 立即尝试刷新代理
            await self._handle_proxy_exception("TimeoutError", str(e))
            raise
        except Exception as e:
            exception_type = type(e).__name__
            self.logger.error(f"获取房源详情失败: {exception_type} - {str(e)}")
            await self._record_api_error(exception_type, f"获取房源详情")
            # 立即尝试刷新代理
            await self._handle_proxy_exception(exception_type, str(e))
            raise

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取API客户端和HTTP连接池的性能统计"""
        try:
            # 获取HTTP客户端性能统计
            http_stats = self.http_client.get_performance_stats()

            # 添加API特定的统计信息
            api_stats = {
                'api_error_count': self.error_count,
                'api_consecutive_errors': self.consecutive_errors,
                'api_has_sent_error_notification': self.has_sent_error_notification,
                'api_first_error_time': self.first_error_time,
                'api_last_admin_notification_time': self.last_admin_notification_time
            }

            # 合并统计信息
            combined_stats = {**http_stats, **api_stats}

            return combined_stats

        except Exception as e:
            self.logger.warning(f"获取性能统计失败: {e}")
            return {"error": str(e)}

    def log_performance_stats(self):
        """记录性能统计到日志"""
        try:
            stats = self.get_performance_stats()

            if 'error' in stats:
                self.logger.warning(f"性能统计获取失败: {stats['error']}")
                return

            # 记录HTTP连接池性能
            self.http_client.log_performance_stats()

            # 记录API特定性能
            self.logger.info(f"API客户端统计: "
                           f"总错误:{stats.get('api_error_count', 0)}, "
                           f"连续错误:{stats.get('api_consecutive_errors', 0)}, "
                           f"已发送错误通知:{stats.get('api_has_sent_error_notification', False)}")

            # 每隔一定请求数报告一次详细性能
            total_requests = stats.get('total_requests', 0)
            if total_requests > 0 and total_requests % self.performance_report_interval == 0:
                self.logger.info(f"📊 性能报告 (第{total_requests}次请求):")
                self.logger.info(f"  HTTP成功率: {stats.get('success_rate', 0):.2%}")
                self.logger.info(f"  平均响应时间: {stats.get('avg_response_time', 0):.3f}s")
                self.logger.info(f"  连接器配置: {stats.get('current_profile', 'unknown')}")
                self.logger.info(f"  连接器成功率: {stats.get('connector_success_rate', 0):.2%}")

        except Exception as e:
            self.logger.warning(f"记录性能统计失败: {e}")

    async def warmup_connections(self):
        """预热API连接"""
        try:
            self.logger.info("开始预热API连接...")
            await self.http_client.warmup_connections()
            self.logger.info("API连接预热完成")
        except Exception as e:
            self.logger.error(f"API连接预热失败: {e}")

    async def optimize_connections(self):
        """优化连接配置"""
        try:
            self.logger.info("开始优化API连接配置...")

            # 获取当前性能统计
            stats = self.get_performance_stats()

            # 如果成功率较低，触发连接池刷新
            success_rate = stats.get('success_rate', 1.0)
            if success_rate < 0.8:
                self.logger.warning(f"API成功率较低 ({success_rate:.2%})，触发连接优化...")
                await self.http_client.force_refresh_connections()

            # 记录优化后的性能
            self.log_performance_stats()

        except Exception as e:
            self.logger.error(f"连接优化失败: {e}")

    def get_connection_health_status(self) -> Dict[str, Any]:
        """获取连接健康状态"""
        try:
            stats = self.get_performance_stats()

            # 评估健康状态
            health_status = {
                'overall_health': 'healthy',
                'issues': [],
                'recommendations': []
            }

            # 检查HTTP成功率
            success_rate = stats.get('success_rate', 1.0)
            if success_rate < 0.8:
                health_status['overall_health'] = 'unhealthy'
                health_status['issues'].append(f'HTTP成功率过低: {success_rate:.2%}')
                health_status['recommendations'].append('建议检查网络连接和代理设置')

            # 检查API错误率
            consecutive_errors = stats.get('api_consecutive_errors', 0)
            if consecutive_errors > 5:
                health_status['overall_health'] = 'degraded'
                health_status['issues'].append(f'API连续错误: {consecutive_errors}次')
                health_status['recommendations'].append('建议检查API服务状态')

            # 检查连接器错误率
            connector_error_rate = stats.get('connector_error_rate', 0.0)
            if connector_error_rate > 0.1:
                health_status['overall_health'] = 'degraded'
                health_status['issues'].append(f'连接器错误率过高: {connector_error_rate:.2%}')
                health_status['recommendations'].append('建议优化连接器配置')

            # 检查响应时间
            avg_response_time = stats.get('avg_response_time', 0.0)
            if avg_response_time > 5.0:
                if health_status['overall_health'] == 'healthy':
                    health_status['overall_health'] = 'degraded'
                health_status['issues'].append(f'平均响应时间过长: {avg_response_time:.2f}s')
                health_status['recommendations'].append('建议检查网络延迟和服务器负载')

            # 添加统计信息
            health_status['stats'] = stats

            return health_status

        except Exception as e:
            return {
                'overall_health': 'unknown',
                'error': str(e)
            }

    async def auto_optimize_if_needed(self):
        """根据健康状态自动优化"""
        try:
            health = self.get_connection_health_status()

            if health['overall_health'] in ['unhealthy', 'degraded']:
                self.logger.warning(f"检测到连接健康状态为 {health['overall_health']}，开始自动优化...")

                for issue in health.get('issues', []):
                    self.logger.warning(f"  问题: {issue}")

                # 触发优化
                await self.optimize_connections()

                # 重新检查健康状态
                new_health = self.get_connection_health_status()
                self.logger.info(f"优化后健康状态: {new_health['overall_health']}")

            return health

        except Exception as e:
            self.logger.error(f"自动优化失败: {e}")
            return None