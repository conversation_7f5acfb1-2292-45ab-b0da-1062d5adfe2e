{"timestamp": "20250731_193739", "backup_dir": "backup_optimization_20250731_193739", "files": [{"original": "core/monitor/house_monitor.py", "backup": "backup_optimization_20250731_193739\\core_monitor_house_monitor.py", "size": 45656}, {"original": "monitor_service/core/monitor.py", "backup": "backup_optimization_20250731_193739\\monitor_service_core_monitor.py", "size": 19993}, {"original": "user_config.json", "backup": "backup_optimization_20250731_193739\\user_config.json", "size": 2969}, {"original": "web/api/monitor.py", "backup": "backup_optimization_20250731_193739\\web_api_monitor.py", "size": 7814}]}