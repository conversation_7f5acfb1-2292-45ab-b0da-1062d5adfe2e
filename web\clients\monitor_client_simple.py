"""
简化的监控服务客户端 - 完全同步实现，避免事件循环问题
"""

import requests
import logging
import os
from typing import Dict, Any, Tuple
from datetime import datetime, timedelta


class SimpleMonitorServiceClient:
    """简化的监控服务HTTP客户端 - 纯同步实现"""

    def __init__(self, base_url: str = None, logger: logging.Logger = None):
        """
        初始化监控服务客户端

        Args:
            base_url: 监控服务的基础URL，默认从环境变量获取
            logger: 日志记录器
        """
        self.base_url = base_url or os.getenv('MONITOR_SERVICE_URL', 'http://127.0.0.1:8090')
        self.logger = logger or logging.getLogger(__name__)
        self._last_health_check = None
        self._health_check_interval = 30  # 健康检查间隔（秒）

        # 重试配置
        self.max_retries = 3
        self.retry_delay = 1.0  # 基础重试延迟（秒）
        self.timeout = 10.0  # 请求超时（秒）

        # 创建会话
        self.session = requests.Session()
        self.session.headers.update({'User-Agent': 'WebConsole-SimpleClient/1.0'})

    def _request(self, method: str, endpoint: str, **kwargs) -> Tuple[bool, Dict[str, Any]]:
        """
        执行HTTP请求，包含重试逻辑

        Args:
            method: HTTP方法
            endpoint: API端点
            **kwargs: 传递给requests的额外参数

        Returns:
            (success: bool, response_data: dict)
        """
        url = f"{self.base_url.rstrip('/')}/{endpoint.lstrip('/')}"

        # 设置默认超时
        kwargs.setdefault('timeout', self.timeout)

        for attempt in range(self.max_retries + 1):
            try:
                response = self.session.request(method, url, **kwargs)

                if response.status_code == 200:
                    try:
                        data = response.json()
                        return True, data
                    except Exception as e:
                        return True, {'status': 'success', 'message': response.text}
                else:
                    error_text = response.text
                    self.logger.debug(f"监控服务请求失败: {response.status_code} - {error_text}")

                    # 对于4xx错误，不重试
                    if 400 <= response.status_code < 500:
                        return False, {
                            'error': f'客户端错误: {response.status_code}',
                            'details': error_text
                        }

                    # 对于5xx错误，继续重试
                    if attempt < self.max_retries:
                        delay = self.retry_delay * (2 ** attempt)
                        self.logger.debug(f"监控服务请求失败，{delay}秒后重试 ({attempt + 1}/{self.max_retries})")
                        import time
                        time.sleep(delay)
                    else:
                        return False, {
                            'error': f'服务器错误: {response.status_code}',
                            'details': error_text
                        }

            except requests.exceptions.ConnectionError as e:
                self.logger.debug(f"监控服务连接异常: {str(e)}")
                if attempt < self.max_retries:
                    delay = self.retry_delay * (2 ** attempt)
                    self.logger.debug(f"连接失败，{delay}秒后重试 ({attempt + 1}/{self.max_retries})")
                    import time
                    time.sleep(delay)
                else:
                    return False, {
                        'error': '连接失败',
                        'details': str(e)
                    }
            except requests.exceptions.Timeout as e:
                self.logger.debug(f"监控服务请求超时: {str(e)}")
                if attempt < self.max_retries:
                    delay = self.retry_delay * (2 ** attempt)
                    self.logger.debug(f"请求超时，{delay}秒后重试 ({attempt + 1}/{self.max_retries})")
                    import time
                    time.sleep(delay)
                else:
                    return False, {
                        'error': '请求超时',
                        'details': str(e)
                    }
            except Exception as e:
                self.logger.debug(f"监控服务请求异常: {str(e)}")
                return False, {
                    'error': '未知错误',
                    'details': str(e)
                }

        # 这里不应该到达，但为了安全起见
        return False, {'error': '重试次数已用尽'}

    def health_check(self, force: bool = False) -> bool:
        """
        健康检查

        Args:
            force: 强制检查，忽略缓存

        Returns:
            服务是否健康
        """
        now = datetime.now()

        # 如果最近检查过且服务健康，直接返回结果
        if not force and self._last_health_check:
            if now - self._last_health_check < timedelta(seconds=self._health_check_interval):
                return True

        success, data = self._request('GET', '/health')

        if success:
            self._last_health_check = now
            self.logger.debug("监控服务健康检查成功")
            return True
        else:
            self._last_health_check = None
            self.logger.debug("监控服务健康检查失败")
            return False

    def get_status(self) -> Tuple[bool, Dict[str, Any]]:
        """获取监控服务状态"""
        return self._request('GET', '/status')

    def start_monitor(self) -> Tuple[bool, Dict[str, Any]]:
        """启动监控"""
        return self._request('POST', '/start')

    def stop_monitor(self) -> Tuple[bool, Dict[str, Any]]:
        """停止监控"""
        return self._request('POST', '/stop')

    def reload_config(self) -> Tuple[bool, Dict[str, Any]]:
        """重载配置"""
        return self._request('POST', '/reload-config')

    def get_proxy_status(self) -> Tuple[bool, Dict[str, Any]]:
        """获取代理状态"""
        return self._request('GET', '/proxy/status')

    def refresh_proxy(self) -> Tuple[bool, Dict[str, Any]]:
        """刷新代理"""
        return self._request('POST', '/proxy/refresh')

    def close(self):
        """关闭HTTP会话"""
        if self.session:
            self.session.close()

    def get_service_url(self) -> str:
        """获取监控服务URL"""
        return self.base_url


# 全局客户端实例，用于在Flask应用中共享
_global_simple_client: SimpleMonitorServiceClient = None


def get_simple_monitor_client(base_url: str = None, logger: logging.Logger = None) -> SimpleMonitorServiceClient:
    """
    获取全局简化监控服务客户端实例

    Args:
        base_url: 监控服务基础URL
        logger: 日志记录器

    Returns:
        SimpleMonitorServiceClient实例
    """
    global _global_simple_client

    if _global_simple_client is None:
        _global_simple_client = SimpleMonitorServiceClient(base_url, logger)

    return _global_simple_client


def close_global_simple_client():
    """关闭全局简化客户端"""
    global _global_simple_client

    if _global_simple_client:
        _global_simple_client.close()
        _global_simple_client = None


# Flask视图的便利函数
def simple_get_status(logger: logging.Logger = None) -> Tuple[bool, Dict[str, Any]]:
    """简化的获取监控状态（Flask视图用）"""
    client = get_simple_monitor_client(logger=logger)
    return client.get_status()


def simple_start_monitor(logger: logging.Logger = None) -> Tuple[bool, Dict[str, Any]]:
    """简化的启动监控（Flask视图用）"""
    client = get_simple_monitor_client(logger=logger)
    return client.start_monitor()


def simple_stop_monitor(logger: logging.Logger = None) -> Tuple[bool, Dict[str, Any]]:
    """简化的停止监控（Flask视图用）"""
    client = get_simple_monitor_client(logger=logger)
    return client.stop_monitor()


def simple_refresh_proxy(logger: logging.Logger = None) -> Tuple[bool, Dict[str, Any]]:
    """简化的刷新代理（Flask视图用）"""
    client = get_simple_monitor_client(logger=logger)
    return client.refresh_proxy()


def simple_health_check(logger: logging.Logger = None) -> bool:
    """简化的健康检查（Flask视图用）"""
    client = get_simple_monitor_client(logger=logger)
    return client.health_check()


def simple_reload_config(logger: logging.Logger = None) -> Tuple[bool, Dict[str, Any]]:
    """简化的重载配置（Flask视图用）"""
    client = get_simple_monitor_client(logger=logger)
    return client.reload_config()