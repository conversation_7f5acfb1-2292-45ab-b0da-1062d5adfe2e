"""
监控API模块 - 简化版，通过HTTP客户端与独立监控服务通信
"""

from datetime import datetime
from flask import Blueprint, jsonify, current_app
from web.utils.decorators import login_required
from web.clients.monitor_client_simple import (
    simple_get_status, simple_start_monitor, simple_stop_monitor,
    simple_refresh_proxy, simple_health_check
)
import json
import os


monitor_api = Blueprint('monitor_api', __name__)


@monitor_api.route('/status')
@login_required
def get_status():
    """获取程序运行状态"""
    try:
        # 通过HTTP客户端获取监控服务状态
        success, data = simple_get_status(current_app.logger)

        if success:
            # 处理监控服务返回的状态数据
            is_running = data.get('is_running', False)
            proxy_info = data.get('proxy', {})

            return jsonify({
                'is_running': is_running,
                'proxy': proxy_info.get('current_proxy'),
                'last_proxy_fetch_time': proxy_info.get('last_fetch_time'),
                'current_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                'remaining_proxies': proxy_info.get('remaining_count', 0),
                'service_status': 'connected'
            })
        else:
            # 监控服务不可用
            current_app.logger.warning(f"监控服务不可用: {data.get('error', '未知错误')}")
            return jsonify({
                'is_running': False,
                'proxy': None,
                'last_proxy_fetch_time': None,
                'current_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                'remaining_proxies': 0,
                'service_status': 'disconnected',
                'error': data.get('error', '监控服务不可用')
            })
    except Exception as e:
        current_app.logger.error(f"获取监控状态失败: {str(e)}")
        return jsonify({
            'error': '获取状态失败',
            'details': str(e),
            'service_status': 'error'
        }), 500


@monitor_api.route('/monitor/start', methods=['POST'])
@login_required
def start_monitor():
    """启动监控程序API"""
    try:
        success, data = simple_start_monitor(current_app.logger)

        if success:
            status = data.get('status', 'success')
            if status == 'already_running':
                return jsonify({'status': 'already running'})
            else:
                return jsonify({'status': 'success'})
        else:
            current_app.logger.error(f"启动监控失败: {data.get('error', '未知错误')}")
            return jsonify({
                'status': 'failed',
                'error': data.get('error', '启动失败')
            }), 500
    except Exception as e:
        current_app.logger.error(f"启动监控异常: {str(e)}")
        return jsonify({
            'status': 'failed',
            'error': '服务异常'
        }), 500


@monitor_api.route('/monitor/stop', methods=['POST'])
@login_required
def stop_monitor():
    """停止监控程序API"""
    try:
        success, data = simple_stop_monitor(current_app.logger)

        if success:
            status = data.get('status', 'success')
            if status == 'not_running':
                return jsonify({'status': 'not running'})
            else:
                return jsonify({'status': 'success'})
        else:
            current_app.logger.error(f"停止监控失败: {data.get('error', '未知错误')}")
            return jsonify({
                'status': 'failed',
                'error': data.get('error', '停止失败')
            }), 500
    except Exception as e:
        current_app.logger.error(f"停止监控异常: {str(e)}")
        return jsonify({
            'status': 'failed',
            'error': '服务异常'
        }), 500


@monitor_api.route('/house_counts')
@login_required
def get_house_counts():
    """获取各房源当前数量"""
    try:
        # 通过数据库直接获取房源数量（这部分逻辑保持不变）
        config_manager = current_app.config_manager
        data_manager = config_manager.get_data_manager()

        # 获取当前启用的监控配置中的房源名称
        active_houses = set()
        enabled_configs = data_manager.get_enabled_monitor_configs()
        for config in enabled_configs:
            active_houses.add(config.get('name', ''))

        # 从数据库获取历史数据
        filtered_counts = {}
        history_data = data_manager.get_history()
        for house_name, count in history_data.items():
            if house_name in active_houses:
                filtered_counts[house_name] = count

        return jsonify({'counts': filtered_counts})
    except Exception as e:
        current_app.logger.error(f"获取房源数量失败: {str(e)}")
        return jsonify({'error': str(e)}), 500


@monitor_api.route('/proxy/refresh', methods=['POST'])
@login_required
def refresh_proxy():
    """强制刷新代理IP"""
    try:
        success, data = simple_refresh_proxy(current_app.logger)

        if success:
            return jsonify({
                'status': data.get('status', 'success'),
                'message': '代理IP已强制更新',
                'old_proxy': data.get('old_proxy'),
                'new_proxy': data.get('new_proxy'),
                'remaining_proxies': data.get('remaining_count', 0)
            })
        else:
            current_app.logger.error(f"刷新代理失败: {data.get('error', '未知错误')}")
            return jsonify({
                'status': 'failed',
                'message': '代理IP更新失败',
                'error': data.get('error', '刷新失败')
            }), 500
    except Exception as e:
        current_app.logger.error(f"刷新代理异常: {str(e)}")
        return jsonify({
            'status': 'failed',
            'error': '服务异常'
        }), 500


@monitor_api.route('/service/health')
@login_required
def check_service_health():
    """检查监控服务健康状态"""
    try:
        is_healthy = simple_health_check(current_app.logger)

        if is_healthy:
            return jsonify({
                'status': 'healthy',
                'message': '监控服务连接正常',
                'timestamp': datetime.now().isoformat()
            })
        else:
            return jsonify({
                'status': 'unhealthy',
                'message': '监控服务连接失败',
                'timestamp': datetime.now().isoformat()
            }), 503
    except Exception as e:
        current_app.logger.error(f"健康检查异常: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': '健康检查失败',
            'error': str(e)
        }), 500


# 兼容性函数（不再需要，但保留接口）
def start_monitor_internal(flask_app=None):
    """启动监控的内部函数 - 兼容性保留"""
    current_app.logger.warning("调用了已弃用的start_monitor_internal函数")
    try:
        success, data = simple_start_monitor(current_app.logger)
        return success and data.get('status') != 'already_running'
    except Exception:
        return False


def stop_monitor_internal(flask_app=None):
    """停止监控的内部函数 - 兼容性保留"""
    current_app.logger.warning("调用了已弃用的stop_monitor_internal函数")
    try:
        success, data = simple_stop_monitor(current_app.logger)
        return success and data.get('status') != 'not_running'
    except Exception:
        return False


@monitor_api.route('/optimization/status')
@login_required
def get_optimization_status():
    """获取监控优化状态"""
    try:
        # 读取配置文件
        config_path = 'user_config.json'
        if not os.path.exists(config_path):
            return jsonify({
                'success': False,
                'message': '配置文件不存在'
            }), 404

        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)

        # 获取优化配置
        optimization = config.get('monitoring_optimization', {})

        return jsonify({
            'success': True,
            'optimization': {
                'enabled': optimization.get('enabled', False),
                'mode': optimization.get('mode', 'legacy'),
                'performance_monitoring': optimization.get('performance_monitoring', {}),
                'check_interval': config.get('check_interval', 60)
            }
        })

    except Exception as e:
        current_app.logger.error(f"获取优化状态时发生错误: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取优化状态失败: {str(e)}'
        }), 500


@monitor_api.route('/optimization/toggle', methods=['POST'])
@login_required
def toggle_optimization():
    """切换监控优化模式"""
    try:
        from flask import request

        # 获取请求参数
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'message': '请求数据为空'
            }), 400

        new_mode = data.get('mode', 'legacy')
        if new_mode not in ['optimized', 'legacy']:
            return jsonify({
                'success': False,
                'message': '无效的监控模式'
            }), 400

        # 读取配置文件
        config_path = 'user_config.json'
        if not os.path.exists(config_path):
            return jsonify({
                'success': False,
                'message': '配置文件不存在'
            }), 404

        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)

        # 更新优化配置
        if 'monitoring_optimization' not in config:
            config['monitoring_optimization'] = {}

        config['monitoring_optimization']['mode'] = new_mode
        config['monitoring_optimization']['enabled'] = (new_mode == 'optimized')

        # 如果切换到优化模式，设置1秒间隔
        if new_mode == 'optimized':
            config['check_interval'] = 1
        else:
            # 传统模式使用60秒间隔
            config['check_interval'] = 60

        # 保存配置文件
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)

        current_app.logger.info(f"监控模式已切换为: {new_mode}")

        return jsonify({
            'success': True,
            'message': f'监控模式已切换为: {"优化模式" if new_mode == "optimized" else "传统模式"}',
            'mode': new_mode,
            'check_interval': config['check_interval']
        })

    except Exception as e:
        current_app.logger.error(f"切换优化模式时发生错误: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'切换优化模式失败: {str(e)}'
        }), 500